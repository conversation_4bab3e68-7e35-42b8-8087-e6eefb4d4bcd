'use client'

import React from 'react'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import { SetCell } from './SetCell'

interface ExerciseSetsGridProps {
  exercise: Pick<ExerciseModel, 'Id' | 'Label' | 'IsBodyweight'>
  sets: (WorkoutLogSerieModel & Partial<WorkoutLogSerieModelRef>)[]
  onSetUpdate: (
    setId: number,
    updates: { reps?: number; weight?: number }
  ) => void
  onSetComplete?: (setIndex: number) => void
  onFinishExercise?: () => void
  onAddSet?: () => void
  unit?: 'kg' | 'lbs'
}

export function ExerciseSetsGrid({
  exercise,
  sets,
  onSetUpdate,
  onSetComplete,
  onFinishExercise,
  onAddSet,
  unit = 'lbs',
}: ExerciseSetsGridProps) {
  // Check if all sets are finished
  const allSetsFinished = sets.length > 0 && sets.every((set) => set.IsFinished)

  // Check if any set has IsNext
  const currentSetIndex = sets.findIndex((set) => set.IsNext)

  if (sets.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] p-4">
        <p className="text-text-secondary mb-4">No sets for this exercise</p>
        {onAddSet && (
          <button
            onClick={onAddSet}
            className="px-4 py-2 bg-brand-primary text-text-inverse rounded-theme font-medium hover:bg-brand-primary/90 transition-colors min-h-[44px]"
          >
            Add set
          </button>
        )}
      </div>
    )
  }

  return (
    <div className="flex flex-col">
      {/* Exercise Name */}
      <div className="px-4 py-3 bg-bg-secondary border-b border-border-primary">
        <h2 className="text-xl font-bold text-text-primary">
          {exercise.Label}
        </h2>
      </div>

      {/* Header Row */}
      <SetCell isHeaderCell unit={unit} />

      {/* Sets */}
      <div className="divide-y divide-border-secondary">
        {sets.map((set, index) => {
          const setNo = parseInt(set.SetNo || `${index + 1}`)
          const isLastSet = index === sets.length - 1
          let weight = 0
          if (set.Weight) {
            weight = unit === 'kg' ? set.Weight.Kg || 0 : set.Weight.Lb || 0
          }

          return (
            <div
              key={set.Id || index}
              className={
                currentSetIndex === index ? 'ring-2 ring-brand-primary' : ''
              }
            >
              <SetCell
                setNo={setNo}
                reps={set.Reps}
                weight={weight}
                isFinished={set.IsFinished}
                isBodyweight={exercise.IsBodyweight}
                isLastSet={isLastSet && !allSetsFinished} // Don't show last set UI when all done
                isExerciseFinished={false} // Don't show finish button in individual cells
                isNext={set.IsNext || false}
                backColor={(set as any).BackColor || 'transparent'}
                weightSingal={(set as any).WeightSingal}
                onRepsChange={(reps) =>
                  onSetUpdate(set.Id || index + 1, { reps })
                }
                onWeightChange={(weight) =>
                  onSetUpdate(set.Id || index + 1, { weight })
                }
                onSetComplete={() => onSetComplete?.(index)}
                onFinishExercise={undefined} // Handle finish at grid level
                onAddSet={isLastSet && !allSetsFinished ? onAddSet : undefined}
                unit={unit}
              />
            </div>
          )
        })}
      </div>

      {/* All sets done message (shown once, not per set) */}
      {allSetsFinished && (
        <div className="px-4 py-6">
          <div className="bg-success/10 rounded-theme p-4">
            <p className="text-center text-success font-medium italic">
              All sets done—congrats!
            </p>
          </div>
          {onFinishExercise && (
            <button
              onClick={onFinishExercise}
              className="w-full mt-4 bg-success text-text-inverse font-bold py-4 rounded-theme text-lg hover:bg-success/90 transition-colors min-h-[66px]"
            >
              Finish exercise
            </button>
          )}
        </div>
      )}
    </div>
  )
}

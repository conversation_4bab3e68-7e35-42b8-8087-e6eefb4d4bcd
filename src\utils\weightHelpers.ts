import type { MultiUnityWeight } from '@/types'

/**
 * Round weight to nearest increment with min/max constraints
 * Matches MAUI RoundToNearestIncrement method
 */
export function roundToNearestIncrement(
  weight: number,
  increment: number,
  min?: number,
  max?: number
): number {
  if (increment === 0) {
    increment = 1
  }

  let rounded = Math.round(weight / increment) * increment

  if (min !== undefined && rounded < min) {
    rounded = min
  }
  if (max !== undefined && rounded > max) {
    rounded = max
  }

  return rounded
}

/**
 * Format weight for display with unit
 * Matches MAUI FormatWeight method
 */
export function formatWeight(weight: MultiUnityWeight | null, isKg: boolean): string {
  if (!weight) {
    return ''
  }

  return isKg
    ? `${Math.round(weight.Kg * 100) / 100} kg`
    : `${Math.round(weight.Lb * 100) / 100} lbs`
}

/**
 * Truncate decimal to specified precision
 * Matches MAUI TruncateDecimal method
 */
export function truncateDecimal(value: number, precision: number): number {
  const step = Math.pow(10, precision)
  const tmp = Math.trunc(step * value)
  return tmp / step
}

/**
 * Get formatted weight signal for display
 * Matches MAUI WeightSingal property logic
 */
export function getWeightSingal(weight: MultiUnityWeight, isKg: boolean): string {
  return isKg
    ? `${Math.round(weight.Kg * 100) / 100}`
    : `${Math.round(weight.Lb * 100) / 100}`
}

/**
 * Get formatted weight double for calculations
 * Matches MAUI WeightDouble property logic
 */
export function getWeightDouble(weight: MultiUnityWeight, isKg: boolean): string {
  return isKg
    ? `${Math.round(weight.Kg * 100) / 100}`
    : `${Math.round(weight.Lb * 100) / 100}`
}

/**
 * Create MultiUnityWeight from value and unit
 */
export function createMultiUnityWeight(value: number, unit: 'kg' | 'lb'): MultiUnityWeight {
  if (unit === 'kg') {
    return {
      Kg: value,
      Lb: value * 2.20462,
    }
  } else {
    return {
      Kg: value / 2.20462,
      Lb: value,
    }
  }
}

/**
 * Convert weight string with comma to dot notation
 * Matches MAUI ReplaceWithDot extension method
 */
export function replaceWithDot(value: string): string {
  return value.replace(/,/g, '.')
}

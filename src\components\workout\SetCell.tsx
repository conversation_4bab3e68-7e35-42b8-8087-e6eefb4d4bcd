'use client'

import React from 'react'

interface SetCellProps {
  isHeaderCell?: boolean
  setNo?: number
  reps?: number
  weight?: number
  isFinished?: boolean
  isBodyweight?: boolean
  isLastSet?: boolean
  isExerciseFinished?: boolean
  isNext?: boolean
  backColor?: string
  weightSingal?: string
  onRepsChange?: (reps: number) => void
  onWeightChange?: (weight: number) => void
  onSetComplete?: () => void
  onFinishExercise?: () => void
  onAddSet?: () => void
  unit?: 'kg' | 'lbs'
}

export function SetCell({
  isHeaderCell = false,
  setNo = 1,
  reps = 0,
  weight = 0,
  isFinished = false,
  isBodyweight = false,
  isLastSet = false,
  isExerciseFinished = false,
  isNext = false,
  backColor = 'transparent',
  weightSingal,
  onRepsChange,
  onWeightChange,
  onSetComplete,
  onFinishExercise,
  onAddSet,
  unit = 'lbs',
}: SetCellProps) {
  const handleRepsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === '' ? 0 : parseInt(e.target.value)
    if (!Number.isNaN(value)) {
      onRepsChange?.(value)
    }
  }

  const handleWeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === '' ? 0 : parseFloat(e.target.value)
    if (!Number.isNaN(value)) {
      onWeightChange?.(value)
    }
  }

  // Format weight display (matching MAUI WeightSingal logic)
  const getFormattedWeight = () => {
    if (weightSingal) return weightSingal
    if (isBodyweight) return ''
    // Format with 2 decimal places like MAUI WeightSingal
    return Number(weight).toFixed(2)
  }

  if (isHeaderCell) {
    return (
      <div className="grid grid-cols-[25px_60px_1fr_25px_1fr] gap-0 px-4 py-3 bg-brand-primary text-text-inverse font-bold">
        <div />
        <div className="text-center">SET</div>
        <div className="text-center">REPS</div>
        <div className="text-center">*</div>
        <div className="text-center">{unit.toUpperCase()}</div>
      </div>
    )
  }

  return (
    <div className={`px-4 ${isFinished ? 'opacity-75' : ''} ${isNext ? 'bg-brand-primary/10' : ''}`}>
      {/* Main Set Row */}
      <div className="grid grid-cols-[25px_60px_1fr_25px_1fr] gap-0 items-center py-2">
        {/* Check/Delete Icon */}
        <div className="flex items-center justify-center">
          {isFinished ? (
            <svg
              className="w-5 h-5 text-success cursor-pointer"
              data-testid="check-icon"
              fill="currentColor"
              viewBox="0 0 20 20"
              onClick={onSetComplete}
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          ) : (
            <svg
              className="w-5 h-5 text-error cursor-pointer hover:text-error/80"
              data-testid="delete-icon"
              fill="currentColor"
              viewBox="0 0 20 20"
              onClick={onSetComplete}
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </div>

        {/* Set Number */}
        <div className="text-center text-lg font-medium text-text-secondary">
          {setNo}
        </div>

        {/* Reps Input */}
        <div className="px-2">
          <input
            type="number"
            value={reps || ''}
            onChange={handleRepsChange}
            disabled={isFinished}
            className="w-full text-center bg-bg-secondary border border-border-primary rounded-theme px-2 py-1 text-lg font-medium disabled:bg-bg-tertiary disabled:text-text-tertiary min-h-[44px]"
            aria-label={`Reps for set ${setNo}`}
            maxLength={4}
          />
        </div>

        {/* Multiplication Symbol */}
        <div className="text-center text-lg text-text-secondary">*</div>

        {/* Weight Input */}
        <div className="px-2">
          <input
            type="number"
            value={getFormattedWeight()}
            onChange={handleWeightChange}
            disabled={isFinished || isBodyweight}
            className="w-full text-center bg-bg-secondary border border-border-primary rounded-theme px-2 py-1 text-lg font-medium disabled:bg-bg-tertiary disabled:text-text-tertiary min-h-[44px]"
            aria-label={`Weight for set ${setNo}`}
          />
        </div>
      </div>

      {/* All sets done message - MultiTrigger: IsLastSet=True AND IsFinished=True AND IsExerciseFinished=False */}
      {isLastSet && isFinished && !isExerciseFinished && (
        <div className="mt-4 bg-success/10 rounded-theme p-4">
          <p className="text-center text-success font-medium italic">
            All sets done—congrats!
          </p>
        </div>
      )}

      {/* Finish Exercise Button - MultiTrigger: IsLastSet=True AND IsFinished=True */}
      {isLastSet && isFinished && onFinishExercise && (
        <div className="mt-4">
          <button
            onClick={onFinishExercise}
            className="w-full bg-success text-text-inverse font-bold py-4 rounded-theme text-lg hover:bg-success/90 transition-colors min-h-[66px]"
            aria-label="Finish this exercise and move to next"
          >
            Finish exercise
          </button>
        </div>
      )}

      {/* Add Set / Skip Exercise buttons - MultiTrigger: IsLastSet=True AND IsExerciseFinished=False */}
      {isLastSet && !isExerciseFinished && onAddSet && (
        <div className="mt-4">
          <button
            onClick={onAddSet}
            className="w-full border-2 border-accent text-text-primary bg-transparent font-medium py-4 rounded-theme hover:bg-accent/10 transition-colors min-h-[60px]"
          >
            Add set
          </button>
        </div>
      )}
    </div>
  )
}

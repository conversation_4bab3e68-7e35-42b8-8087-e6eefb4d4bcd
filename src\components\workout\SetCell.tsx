'use client'

import React from 'react'

interface SetCellProps {
  isHeaderCell?: boolean
  setNo?: number
  reps?: number
  weight?: number
  isFinished?: boolean
  isBodyweight?: boolean
  isLastSet?: boolean
  isExerciseFinished?: boolean
  isNext?: boolean
  backColor?: string
  weightSingal?: string
  onRepsChange?: (reps: number) => void
  onWeightChange?: (weight: number) => void
  onFinishExercise?: () => void
  onAddSet?: () => void
  unit?: 'kg' | 'lbs'
}

export function SetCell({
  isHeaderCell = false,
  setNo = 1,
  reps = 0,
  weight = 0,
  isFinished = false,
  isBodyweight = false,
  isLastSet = false,
  isExerciseFinished = false,
  isNext = false,
  backColor = 'transparent',
  weightSingal,
  onRepsChange,
  onWeightChange,
  onFinishExercise,
  onAddSet,
  unit = 'lbs',
}: SetCellProps) {
  const handleRepsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === '' ? 0 : parseInt(e.target.value)
    if (!Number.isNaN(value)) {
      onRepsChange?.(value)
    }
  }

  const handleWeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === '' ? 0 : parseFloat(e.target.value)
    if (!Number.isNaN(value)) {
      onWeightChange?.(value)
    }
  }

  // Calculate background color based on state (matching MAUI BackColor logic)
  const getBackgroundColor = () => {
    if (isFinished || isNext) {
      // Match MAUI: Android uses #4D0C2432, iOS uses #660C2432
      return 'rgba(12, 36, 50, 0.3)' // Using Android color as default
    }
    return backColor || 'transparent'
  }

  // Format weight display (matching MAUI WeightSingal logic)
  const getFormattedWeight = () => {
    if (weightSingal) return weightSingal
    if (isBodyweight) return ''
    return weight.toFixed(2)
  }

  if (isHeaderCell) {
    return (
      <div className="bg-gradient-to-b from-blue-600 to-blue-700 px-4 py-3 mx-1 rounded-none">
        <div className="grid grid-cols-[25px_60px_0.77fr_25px_0.77fr] gap-0 items-center">
          <div />
          <div className="text-center text-white text-lg font-bold">SET</div>
          <div className="text-center text-white text-lg font-bold">REPS</div>
          <div />
          <div className="text-center text-white text-lg font-bold">{unit.toUpperCase()}</div>
        </div>
      </div>
    )
  }

  return (
    <div
      className="bg-gradient-to-b from-blue-600 to-blue-700 px-4 py-2 mx-1 rounded-none"
      style={{ backgroundColor: getBackgroundColor() }}
    >
      {/* Main Set Row */}
      <div className="grid grid-cols-[25px_60px_0.77fr_25px_0.77fr] gap-0 items-center py-1">
        {/* Check/Delete Icon */}
        <div className="flex items-center justify-center my-1">
          {isFinished ? (
            <svg
              className="w-5 h-5 text-green-400"
              data-testid="check-icon"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          ) : (
            <svg
              className="w-5 h-5 text-red-400"
              data-testid="delete-icon"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </div>

        {/* Set Number */}
        <div className="text-center text-xl font-medium" style={{ color: '#AAFFFFFF' }}>
          {setNo}
        </div>

        {/* Reps Input */}
        <div className="px-2">
          <input
            type="number"
            value={reps || ''}
            onChange={handleRepsChange}
            disabled={isFinished}
            className="w-full text-center text-xl font-medium px-2 py-1 border-0 rounded-none min-h-[44px]"
            style={{
              backgroundColor: getBackgroundColor(),
              color: '#AAFFFFFF',
              maxLength: 4
            }}
            aria-label={`Reps for set ${setNo}`}
            maxLength={4}
          />
        </div>

        {/* Multiplication Symbol */}
        <div className="text-center text-xl mt-1" style={{ color: '#AAFFFFFF' }}>*</div>

        {/* Weight Input */}
        <div className="px-2">
          <input
            type="number"
            value={getFormattedWeight()}
            onChange={handleWeightChange}
            disabled={isFinished || isBodyweight}
            className="w-full text-center text-xl font-medium px-2 py-1 border-0 rounded-none min-h-[44px]"
            style={{
              backgroundColor: getBackgroundColor(),
              color: '#AAFFFFFF'
            }}
            aria-label={`Weight for set ${setNo}`}
          />
        </div>
      </div>

      {/* All sets done message - MultiTrigger: IsLastSet=True AND IsFinished=True AND IsExerciseFinished=False */}
      {isLastSet && isFinished && !isExerciseFinished && (
        <div className="mt-5 mb-1 mx-0 px-3 py-3 rounded-md" style={{ backgroundColor: getBackgroundColor() }}>
          <p className="text-center text-white font-medium italic text-xl">
            All sets done—congrats!
          </p>
        </div>
      )}

      {/* Finish Exercise Button - MultiTrigger: IsLastSet=True AND IsFinished=True */}
      {isLastSet && isFinished && onFinishExercise && (
        <div className="mt-3 mb-0 mx-1">
          <button
            onClick={onFinishExercise}
            className="w-full bg-gradient-to-b from-green-400 to-green-500 text-gray-900 font-bold py-4 rounded-md text-xl hover:from-green-300 hover:to-green-400 transition-colors min-h-[66px]"
          >
            Finish exercise
          </button>
        </div>
      )}

      {/* Add Set Button - MultiTrigger: IsLastSet=True AND IsExerciseFinished=False */}
      {isLastSet && !isExerciseFinished && onAddSet && (
        <div className="mt-3 mb-5 mx-0">
          <div className="grid grid-cols-1 gap-2 h-15">
            <button
              onClick={onAddSet}
              className="w-full border border-green-300 text-white bg-transparent font-medium py-4 rounded-md hover:bg-green-300/10 transition-colors min-h-[60px] mx-1"
            >
              Add set
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

import type {
  ExerciseModel,
  RecommendationModel,
  MultiUnityWeight,
} from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import { roundToNearestIncrement, formatWeight, createMultiUnityWeight } from './weightHelpers'

/**
 * Creates workout sets based on exercise and recommendation model
 * Exactly matches the MAUI CreateWorkoutSets functionality
 */
export function createWorkoutSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  isKg: boolean
): WorkoutLogSerieModelRef[] {
  const setList: WorkoutLogSerieModelRef[] = []

  // 1. Generate Warm-up Sets
  if (recommendation.WarmUpsList && recommendation.WarmUpsList.length > 0) {
    for (let i = 0; i < recommendation.WarmUpsList.length; i++) {
      const warmup = recommendation.WarmUpsList[i]
      setList.push({
        Id: exercise.Id,
        ExerciseId: exercise.Id,
        Weight: warmup.WarmUpWeightSet || { Kg: 0, Lb: 0 },
        IsWarmups: true,
        Reps: warmup.WarmUpReps,
        PreviousReps: warmup.WarmUpReps,
        SetNo: 'W',
        IsLastWarmupSet: i === recommendation.WarmUpsList.length - 1,
        IsHeaderCell: i === 0,
        HeaderImage: '',
        HeaderTitle: '',
        ExerciseName: exercise.Label,
        EquipmentId: exercise.EquipmentId,
        SetTitle: i === 0 ? "Let's warm up:" : 
                  (i === recommendation.WarmUpsList.length - 1 ? 'Last warm-up set:' : ''),
        IsFinished: false,
        IsNext: i === 0,
        LastTimeSet: '',
        IsTimeBased: exercise.IsTimeBased,
        IsBodyweight: exercise.IsBodyweight,
        IsNormalset: exercise.IsNormalSets,
        BackColor: 'transparent',
        Increments: recommendation.Increments || { Kg: 2.5, Lb: 5 },
        Min: recommendation.Min || { Kg: 0, Lb: 0 },
        Max: recommendation.Max || { Kg: 1000, Lb: 2000 },
        BodypartId: exercise.BodyPartId,
        IsUnilateral: exercise.IsUnilateral,
        IsFlexibility: exercise.IsFlexibility,
        IsActive: false,
        IsEditing: false,
        IsFirstSide: false,
        IsFirstSetFinished: false,
        IsFirstWorkSet: false,
        IsExerciseFinished: false,
        IsJustSetup: false,
        ShouldUpdateIncrement: false,
        IsBackOffSet: false,
        IsNextBackOffSet: false,
        IsDropSet: false,
        IsMaxChallenge: false,
        IsAssisted: exercise.IsAssisted,
        IsTimerOff: false,
        IsSizeChanged: false,
        ShowWorkTimer: false,
        NbPause: 0,
        OneRMProgress: 0,
        PreviousWeight: { Kg: 0, Lb: 0 },
        Speed: 0,
        VideoUrl: exercise.VideoUrl,
        ShowPlusTooltip: false,
        ShowSuperSet3: false,
        ShowSuperSet2: false,
        IsLastSet: false,
      } as WorkoutLogSerieModelRef)
    }
  }

  // 2. Generate Work Sets based on type
  if (recommendation.IsPyramid) {
    generatePyramidSets(exercise, recommendation, setList, isKg)
  } else if (recommendation.IsReversePyramid) {
    generateReversePyramidSets(exercise, recommendation, setList, isKg)
  } else if (recommendation.IsBackOffSet) {
    generateBackOffSets(exercise, recommendation, setList, isKg)
  } else if (recommendation.NbPauses > 0) {
    generateRestPauseSets(exercise, recommendation, setList, isKg)
  } else {
    generateNormalSets(exercise, recommendation, setList, isKg)
  }

  // 3. Set additional properties for all sets
  setList.forEach((set) => {
    set.IsBodyweight = exercise.IsBodyweight
    set.IsTimeBased = exercise.IsTimeBased
    set.IsUnilateral = exercise.IsUnilateral
    set.BodypartId = exercise.BodyPartId
    set.Increments = recommendation.Increments || { Kg: 2.5, Lb: 5 }
    set.Min = recommendation.Min || { Kg: 0, Lb: 0 }
    set.Max = recommendation.Max || { Kg: 1000, Lb: 2000 }
    set.IsFlexibility = exercise.IsFlexibility
    set.EquipmentId = exercise.EquipmentId
  })

  // 4. Calculate IsNext for unfinished sets
  const firstUnfinishedIndex = setList.findIndex((set) => !set.IsFinished)
  if (firstUnfinishedIndex !== -1) {
    setList[firstUnfinishedIndex].IsNext = true
  }

  return setList
}

/**
 * Create base work set with common properties
 */
function createBaseWorkSet(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setNumber: number,
  existingSetsCount: number,
  isKg: boolean
): WorkoutLogSerieModelRef {
  return {
    ExerciseName: exercise.Label,
    ExerciseId: exercise.Id,
    Id: exercise.Id,
    IsWarmups: false,
    SetNo: `${setNumber + 1}`,
    IsHeaderCell: existingSetsCount === 0 && setNumber === 0,
    IsFirstWorkSet: setNumber === 0,
    IsFinished: false,
    IsNext: false,
    Weight: recommendation.Weight,
    Reps: recommendation.Reps,
    SetTitle: '',
    LastTimeSet: setNumber === 0 && recommendation.FirstWorkSetReps && recommendation.FirstWorkSetWeight
      ? `Last time: ${recommendation.FirstWorkSetReps} x ${formatWeight(recommendation.FirstWorkSetWeight, isKg)}`
      : '',
    IsTimeBased: exercise.IsTimeBased,
    IsBodyweight: exercise.IsBodyweight,
    IsNormalset: exercise.IsNormalSets,
    BackColor: 'transparent',
    Increments: recommendation.Increments || { Kg: 2.5, Lb: 5 },
    Min: recommendation.Min || { Kg: 0, Lb: 0 },
    Max: recommendation.Max || { Kg: 1000, Lb: 2000 },
    BodypartId: exercise.BodyPartId,
    IsUnilateral: exercise.IsUnilateral,
    IsFlexibility: exercise.IsFlexibility,
    EquipmentId: exercise.EquipmentId,
    IsActive: false,
    IsEditing: false,
    IsFirstSide: false,
    IsLastWarmupSet: false,
    IsFirstSetFinished: false,
    IsExerciseFinished: false,
    IsJustSetup: false,
    ShouldUpdateIncrement: false,
    IsBackOffSet: false,
    IsNextBackOffSet: false,
    IsDropSet: false,
    IsMaxChallenge: false,
    IsAssisted: exercise.IsAssisted,
    IsTimerOff: false,
    IsSizeChanged: false,
    ShowWorkTimer: false,
    NbPause: 0,
    OneRMProgress: 0,
    PreviousReps: 0,
    PreviousWeight: { Kg: 0, Lb: 0 },
    Speed: 0,
    HeaderImage: '',
    HeaderTitle: '',
    VideoUrl: exercise.VideoUrl,
    ShowPlusTooltip: false,
    ShowSuperSet3: false,
    ShowSuperSet2: false,
    IsLastSet: false,
  } as WorkoutLogSerieModelRef
}

/**
 * Generate pyramid sets (weight increases, reps decrease)
 */
function generatePyramidSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(exercise, recommendation, j, setList.length, isKg)
    
    if (j === 0) {
      // First work set
      rec.SetTitle = recommendation.Series > 1 ? 'Pyramid set:' : ''
    } else {
      // Subsequent pyramid sets
      const lastSet = setList[setList.length - 1]
      if (lastSet?.Weight) {
        const weightIncrease = lastSet.Weight.Kg * 0.1 // 10% increase
        const newWeight = lastSet.Weight.Kg + weightIncrease
        
        rec.Weight = createMultiUnityWeight(
          roundToNearestIncrement(newWeight, recommendation.Increments?.Kg || 2.5),
          'kg'
        )
        rec.Reps = Math.max(1, (lastSet.Reps || 0) - 2) // 2 reps less
        rec.SetTitle = ''
      }
    }
    
    rec.IsLastSet = j === recommendation.Series - 1
    setList.push(rec)
  }
}

/**
 * Generate reverse pyramid sets (weight decreases, reps increase)
 */
function generateReversePyramidSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(exercise, recommendation, j, setList.length, isKg)
    
    if (j === 0) {
      // First work set (heaviest)
      rec.SetTitle = recommendation.Series > 1 ? 'Reverse pyramid:' : ''
    } else {
      // Subsequent reverse pyramid sets
      const lastSet = setList[setList.length - 1]
      if (lastSet?.Weight) {
        const weightDecrease = lastSet.Weight.Kg * 0.1 // 10% decrease
        const newWeight = lastSet.Weight.Kg - weightDecrease
        
        rec.Weight = createMultiUnityWeight(
          roundToNearestIncrement(newWeight, recommendation.Increments?.Kg || 2.5),
          'kg'
        )
        rec.Reps = (lastSet.Reps || 0) + 2 // 2 reps more
        rec.SetTitle = ''
      }
    }
    
    rec.IsLastSet = j === recommendation.Series - 1
    setList.push(rec)
  }
}

/**
 * Generate back-off sets (lighter sets after main set)
 */
function generateBackOffSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(exercise, recommendation, j, setList.length, isKg)
    
    if (j === 0) {
      // Main set
      rec.SetTitle = ''
    } else {
      // Back-off sets (20% lighter)
      rec.Weight = recommendation.BackOffSetWeight || 
        createMultiUnityWeight(recommendation.Weight.Kg * 0.8, 'kg')
      rec.Reps = recommendation.Reps + 2
      rec.SetTitle = 'Back-off set:'
      rec.IsBackOffSet = true
    }
    
    rec.IsLastSet = j === recommendation.Series - 1
    setList.push(rec)
  }
}

/**
 * Generate rest-pause set
 */
function generateRestPauseSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  const rec = createBaseWorkSet(exercise, recommendation, 0, setList.length, isKg)
  
  rec.SetTitle = 'Rest-pause'
  rec.NbPause = recommendation.NbPauses
  rec.IsFirstWorkSet = true
  rec.IsLastSet = true
  
  setList.push(rec)
}

/**
 * Generate normal work sets
 */
function generateNormalSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(exercise, recommendation, j, setList.length, isKg)
    
    rec.SetTitle = j === 0 && recommendation.Series > 1 ? 'Working sets:' : ''
    rec.IsNormalset = true
    rec.IsLastSet = j === recommendation.Series - 1
    
    setList.push(rec)
  }
}
